pipeline {
    agent any
    stages {

         stage('Git clone') {
            steps(if !fileExists('your-repo')) {
                sh 'git clone https://github.com/your-repo.git'
                sh 'cd your-repo'
            }
        }
        stage('Build') {
            steps {
                sh 'npm install'
                sh 'npm run build'
            }
        }
        stage('Test') {
            steps {
                sh 'npm test'
            }
        }
        stage('Deploy') {
            steps {
                sh 'npm run deploy'
            }
        }
    }
}